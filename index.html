<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>한국투자증권 미국주식 자동매매 시스템</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 1200px;
            min-height: 600px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .main-content {
            padding: 30px;
        }

        .login-section {
            display: block;
        }

        .trading-section {
            display: none;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);
        }

        .status-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            font-weight: 600;
            color: #666;
        }

        .status-value {
            color: #333;
        }

        .strategy-panel {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .strategy-title {
            font-weight: 600;
            color: #856404;
            margin-bottom: 15px;
        }

        .strategy-item {
            margin-bottom: 8px;
            color: #856404;
            font-size: 14px;
        }

        .log-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 8px;
            font-size: 12px;
            font-family: 'Courier New', monospace;
        }

        .log-time {
            color: #666;
        }

        .log-info {
            color: #007bff;
        }

        .log-success {
            color: #28a745;
        }

        .log-warning {
            color: #ffc107;
        }

        .log-error {
            color: #dc3545;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 20px;
                max-width: none;
            }
        }

        .alert {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 한국투자증권 미국주식 자동매매</h1>
            <p>Korea Investment & Securities US Stock Auto Trading System</p>
            <div style="margin-top: 10px; font-size: 12px; opacity: 0.8;">
                ✅ Node.js 서버 연결됨 | 🔒 CORS 보안 해결됨 | 🌐 완전한 API 기능 지원
            </div>
        </div>

        <div class="main-content">
            <!-- 로그인 섹션 -->
            <div class="login-section" id="loginSection">
                <h2 style="margin-bottom: 20px; color: #333;">API 인증 설정</h2>
                
                <div class="form-group">
                    <label for="environment">거래 환경</label>
                    <select id="environment">
                        <option value="vps">모의투자</option>
                        <option value="prod">실전투자</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="appKey">App Key</label>
                    <input type="text" id="appKey" placeholder="한국투자증권에서 발급받은 App Key를 입력하세요">
                </div>

                <div class="form-group">
                    <label for="appSecret">App Secret</label>
                    <input type="password" id="appSecret" placeholder="한국투자증권에서 발급받은 App Secret을 입력하세요">
                </div>

                <div class="form-group">
                    <label for="accountNumber">계좌번호 (앞 8자리)</label>
                    <input type="text" id="accountNumber" placeholder="계좌번호 앞 8자리를 입력하세요" maxlength="8">
                </div>

                <div class="form-group">
                    <label for="accountProduct">계좌상품코드</label>
                    <select id="accountProduct">
                        <option value="01">종합계좌</option>
                        <option value="03">국내선물옵션</option>
                        <option value="08">해외선물옵션</option>
                    </select>
                </div>

                <button class="btn" onclick="login()" id="loginBtn">
                    🔐 API 인증 및 로그인
                </button>

                <div id="loginAlert" style="margin-top: 20px;"></div>
            </div>

            <!-- 거래 섹션 -->
            <div class="trading-section" id="tradingSection">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2 style="color: #333;">자동매매 대시보드</h2>
                    <button class="btn btn-danger" onclick="logout()">로그아웃</button>
                </div>

                <!-- 매매 설정 패널 -->
                <div class="strategy-panel">
                    <div class="strategy-title">⚙️ 매매 설정</div>

                    <div class="grid" style="margin-bottom: 20px;">
                        <div>
                            <label for="buyAmount" style="font-weight: 600; margin-bottom: 5px; display: block;">💰 매수 금액 ($)</label>
                            <input type="number" id="buyAmount" value="1000" min="100" max="10000" step="100"
                                   onchange="onTradingSettingsChange()"
                                   style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label for="stopLoss" style="font-weight: 600; margin-bottom: 5px; display: block;">📉 손절 (%)</label>
                            <input type="number" id="stopLoss" value="-5" min="-20" max="-1" step="0.5"
                                   onchange="onTradingSettingsChange()"
                                   style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="font-weight: 600; margin-bottom: 10px; display: block;">📊 부분 익절 설정</label>
                        <div id="profitTakingSettings">
                            <div class="profit-taking-row" style="display: flex; gap: 10px; margin-bottom: 8px; align-items: center;">
                                <span style="width: 60px; font-size: 12px;">1차 익절:</span>
                                <input type="number" class="profit-rate" value="10" min="1" max="100" step="1" placeholder="수익률%"
                                       onchange="onTradingSettingsChange()"
                                       style="width: 80px; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                                <span style="font-size: 12px;">%에서</span>
                                <input type="number" class="profit-quantity" value="50" min="10" max="100" step="10" placeholder="매도%"
                                       onchange="onTradingSettingsChange()"
                                       style="width: 80px; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                                <span style="font-size: 12px;">% 매도</span>
                            </div>
                            <div class="profit-taking-row" style="display: flex; gap: 10px; margin-bottom: 8px; align-items: center;">
                                <span style="width: 60px; font-size: 12px;">2차 익절:</span>
                                <input type="number" class="profit-rate" value="20" min="1" max="100" step="1" placeholder="수익률%"
                                       onchange="onTradingSettingsChange()"
                                       style="width: 80px; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                                <span style="font-size: 12px;">%에서</span>
                                <input type="number" class="profit-quantity" value="30" min="10" max="100" step="10" placeholder="매도%"
                                       onchange="onTradingSettingsChange()"
                                       style="width: 80px; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                                <span style="font-size: 12px;">% 매도</span>
                            </div>
                            <div class="profit-taking-row" style="display: flex; gap: 10px; margin-bottom: 8px; align-items: center;">
                                <span style="width: 60px; font-size: 12px;">3차 익절:</span>
                                <input type="number" class="profit-rate" value="30" min="1" max="100" step="1" placeholder="수익률%"
                                       onchange="onTradingSettingsChange()"
                                       style="width: 80px; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                                <span style="font-size: 12px;">%에서</span>
                                <input type="number" class="profit-quantity" value="20" min="10" max="100" step="10" placeholder="매도%"
                                       onchange="onTradingSettingsChange()"
                                       style="width: 80px; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                                <span style="font-size: 12px;">% 매도</span>
                            </div>
                        </div>
                        <button onclick="addProfitTakingRow()" style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 12px; margin-top: 5px;">+ 익절 단계 추가</button>
                    </div>

                    <div style="background: #e8f5e8; padding: 10px; border-radius: 4px; font-size: 12px; color: #2d5a2d;">
                        <strong>📋 익절 규칙:</strong><br>
                        • 1차 익절 후 나머지 물량은 매입단가에서 손절 설정<br>
                        • 각 익절 단계는 순차적으로 실행됨<br>
                        • 최종 물량은 최고가 대비 -18% 익절
                    </div>

                    <div style="margin-top: 15px; text-align: center;">
                        <button id="applyTradingBtn" onclick="applyTradingSettings()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; font-weight: 600; cursor: pointer;">
                            ⚙️ 매매 설정 적용
                        </button>
                        <div id="tradingSettingsStatus" style="margin-top: 10px; font-size: 12px; color: #666;">
                            설정을 변경한 후 "매매 설정 적용" 버튼을 클릭하세요.
                        </div>
                    </div>
                </div>

                <!-- 모니터링 설정 패널 -->
                <div class="strategy-panel">
                    <div class="strategy-title">🔍 모니터링 설정</div>

                    <div class="grid" style="margin-bottom: 15px;">
                        <div>
                            <label for="minRiseRate" style="font-weight: 600; margin-bottom: 5px; display: block;">📈 최소 상승률 (%)</label>
                            <input type="number" id="minRiseRate" value="20" min="5" max="100" step="1"
                                   onchange="onMonitoringSettingsChange()"
                                   style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label for="minVolume" style="font-weight: 600; margin-bottom: 5px; display: block;">💰 최소 거래금액 (만불)</label>
                            <input type="number" id="minVolume" value="80" min="10" max="1000" step="10"
                                   onchange="onMonitoringSettingsChange()"
                                   style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <div style="background: #e8f5e8; padding: 10px; border-radius: 4px; border-left: 4px solid #28a745;">
                            <strong style="color: #155724;">🌐 시장 전체 실시간 스캔</strong>
                            <div style="font-size: 12px; color: #155724; margin-top: 5px;">
                                전체 미국 주식 시장에서 조건을 충족하는 종목을 실시간으로 검색하여 모니터링합니다.
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label for="maxMonitoringStocks" style="font-weight: 600; margin-bottom: 5px; display: block;">🎯 최대 모니터링 종목 수</label>
                        <input type="number" id="maxMonitoringStocks" value="20" min="5" max="50" step="5"
                               onchange="onMonitoringSettingsChange()"
                               style="width: 200px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div style="background: #e8f5e8; padding: 10px; border-radius: 4px; font-size: 12px; color: #2d5a2d;">
                        <strong>📋 시장 전체 스캔 규칙:</strong><br>
                        • 전체 미국 주식 시장에서 실시간 검색<br>
                        • 상승률 + 거래금액 조건 동시 충족 종목만 선별<br>
                        • 상승률 순으로 자동 정렬<br>
                        • 스캔 주기: 5분마다 자동 업데이트<br>
                        • 한국투자증권 조건검색 API 사용
                    </div>

                    <div style="margin-top: 15px; text-align: center;">
                        <button id="applyMonitoringBtn" onclick="applyMonitoringSettings()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; font-weight: 600; cursor: pointer; margin-right: 10px;">
                            ⚙️ 모니터링 설정 적용
                        </button>
                        <button onclick="scanStocksNow()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; font-weight: 600; cursor: pointer;">
                            🔍 즉시 스캔
                        </button>
                        <div id="monitoringSettingsStatus" style="margin-top: 10px; font-size: 12px; color: #666;">
                            설정을 변경한 후 "모니터링 설정 적용" 버튼을 클릭하세요.
                        </div>
                    </div>
                </div>

                <!-- 현재 모니터링 종목 표시 -->
                <div class="strategy-panel">
                    <div class="strategy-title">📊 현재 모니터링 종목</div>
                    <div id="currentWatchList" style="font-size: 12px; color: #666;">
                        로딩 중...
                    </div>
                </div>

                <div class="grid">
                    <!-- 시스템 상태 -->
                    <div>
                        <h3 style="margin-bottom: 15px; color: #333;">시스템 상태</h3>
                        <div class="status-panel">
                            <div class="status-item">
                                <span class="status-label">거래 환경:</span>
                                <span class="status-value" id="currentEnv">-</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">계좌번호:</span>
                                <span class="status-value" id="currentAccount">-</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">자동매매 상태:</span>
                                <span class="status-value" id="tradingStatus">중지됨</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">모니터링 종목 수:</span>
                                <span class="status-value" id="monitoringCount">0</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">계좌잔액:</span>
                                <span class="status-value" id="accountBalance">-</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">가용금액:</span>
                                <span class="status-value" id="availableBalance">-</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">보유 포지션:</span>
                                <span class="status-value" id="positionCount">0</span>
                            </div>
                        </div>

                        <div style="margin-top: 20px;">
                            <button class="btn btn-success" onclick="startTrading()" id="startBtn">
                                ▶️ 자동매매 시작
                            </button>
                            <button class="btn btn-danger" onclick="stopTrading()" id="stopBtn" disabled>
                                ⏹️ 자동매매 중지
                            </button>
                            <button class="btn" onclick="getAccountBalance()" style="background: #17a2b8; margin-top: 10px;">
                                💰 잔액 새로고침
                            </button>
                        </div>
                    </div>

                    <!-- 포지션 현황 -->
                    <div>
                        <h3 style="margin-bottom: 15px; color: #333;">포지션 현황</h3>
                        <div class="status-panel" id="positionPanel">
                            <div style="text-align: center; color: #666; padding: 20px;">
                                현재 보유 중인 포지션이 없습니다.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 로그 패널 -->
                <div style="margin-top: 30px;">
                    <h3 style="margin-bottom: 15px; color: #333;">거래 로그</h3>
                    <div class="log-panel" id="logPanel">
                        <div class="log-entry">
                            <span class="log-time">[시스템]</span>
                            <span class="log-info">자동매매 시스템이 준비되었습니다.</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 전역 변수
        let accessToken = '';
        let isTrading = false;
        let monitoringInterval = null;
        let positions = [];
        let config = {
            environment: 'vps',
            appKey: '',
            appSecret: '',
            accountNumber: '',
            accountProduct: '01'
        };

        // 매매 설정
        let tradingSettings = {
            buyAmount: 1000,
            stopLoss: -5,
            profitTakingSteps: [
                { rate: 10, quantity: 50 },
                { rate: 20, quantity: 30 },
                { rate: 30, quantity: 20 }
            ]
        };

        // 모니터링 설정 (동적 스캔 전용)
        let monitoringSettings = {
            minRiseRate: 20,        // 최소 상승률 (%)
            minVolume: 80,          // 최소 거래금액 (만불)
            maxStocks: 20           // 최대 모니터링 종목 수
        };

        // 현재 모니터링 종목 리스트 (시장 전체 스캔으로 업데이트됨)
        let currentWatchList = [];

        // API 기본 설정 (Node.js 프록시 서버 사용)
        // 모든 API 호출은 /api/* 경로를 통해 Node.js 서버에서 프록시됩니다

        // 로그 추가 함수
        function addLog(message, type = 'info') {
            const logPanel = document.getElementById('logPanel');
            const now = new Date();
            const timeStr = now.toLocaleTimeString('ko-KR');

            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-time">[${timeStr}]</span>
                <span class="log-${type}">${message}</span>
            `;

            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }

        // 알림 표시 함수
        function showAlert(message, type = 'info') {
            const alertDiv = document.getElementById('loginAlert');
            alertDiv.innerHTML = `<div class="alert alert-${type}">${message}</div>`;

            setTimeout(() => {
                alertDiv.innerHTML = '';
            }, 5000);
        }

        // 익절 단계 추가 함수
        function addProfitTakingRow() {
            const container = document.getElementById('profitTakingSettings');
            const rowCount = container.children.length + 1;

            if (rowCount > 5) {
                alert('최대 5단계까지만 설정 가능합니다.');
                return;
            }

            const newRow = document.createElement('div');
            newRow.className = 'profit-taking-row';
            newRow.style.cssText = 'display: flex; gap: 10px; margin-bottom: 8px; align-items: center;';
            newRow.innerHTML = `
                <span style="width: 60px; font-size: 12px;">${rowCount}차 익절:</span>
                <input type="number" class="profit-rate" value="${rowCount * 10 + 30}" min="1" max="100" step="1" placeholder="수익률%"
                       onchange="onTradingSettingsChange()"
                       style="width: 80px; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                <span style="font-size: 12px;">%에서</span>
                <input type="number" class="profit-quantity" value="100" min="10" max="100" step="10" placeholder="매도%"
                       onchange="onTradingSettingsChange()"
                       style="width: 80px; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                <span style="font-size: 12px;">% 매도</span>
                <button onclick="removeProfitTakingRow(this)" style="background: #dc3545; color: white; border: none; padding: 2px 6px; border-radius: 3px; font-size: 10px;">삭제</button>
            `;
            container.appendChild(newRow);
        }

        // 익절 단계 삭제 함수
        function removeProfitTakingRow(button) {
            button.parentElement.remove();
            onTradingSettingsChange(); // 변경 감지
        }

        // 매매 설정 업데이트 함수 (내부용)
        function updateTradingSettings() {
            tradingSettings.buyAmount = parseFloat(document.getElementById('buyAmount').value) || 1000;
            tradingSettings.stopLoss = parseFloat(document.getElementById('stopLoss').value) || -5;

            // 익절 단계 설정 수집
            const profitRows = document.querySelectorAll('.profit-taking-row');
            tradingSettings.profitTakingSteps = [];

            profitRows.forEach(row => {
                const rate = parseFloat(row.querySelector('.profit-rate').value) || 0;
                const quantity = parseFloat(row.querySelector('.profit-quantity').value) || 0;
                if (rate > 0 && quantity > 0) {
                    tradingSettings.profitTakingSteps.push({ rate, quantity });
                }
            });

            // 수익률 순으로 정렬
            tradingSettings.profitTakingSteps.sort((a, b) => a.rate - b.rate);
        }

        // 매매 설정 적용 함수 (버튼 클릭용)
        function applyTradingSettings() {
            const btn = document.getElementById('applyTradingBtn');
            const status = document.getElementById('tradingSettingsStatus');

            // 버튼 상태 변경
            btn.disabled = true;
            btn.innerHTML = '⏳ 적용 중...';
            btn.style.background = '#6c757d';

            // 설정 유효성 검사
            const buyAmount = parseFloat(document.getElementById('buyAmount').value);
            const stopLoss = parseFloat(document.getElementById('stopLoss').value);

            if (!buyAmount || buyAmount < 100 || buyAmount > 10000) {
                showAlert('매수 금액은 $100 ~ $10,000 범위로 설정해주세요.', 'error');
                resetTradingButton();
                return;
            }

            if (!stopLoss || stopLoss > -1 || stopLoss < -20) {
                showAlert('손절률은 -1% ~ -20% 범위로 설정해주세요.', 'error');
                resetTradingButton();
                return;
            }

            // 익절 단계 유효성 검사
            const profitRows = document.querySelectorAll('.profit-taking-row');
            let totalQuantity = 0;
            let hasError = false;

            profitRows.forEach((row, index) => {
                const rate = parseFloat(row.querySelector('.profit-rate').value) || 0;
                const quantity = parseFloat(row.querySelector('.profit-quantity').value) || 0;

                if (rate <= 0 || rate > 100) {
                    showAlert(`${index + 1}차 익절 수익률은 1% ~ 100% 범위로 설정해주세요.`, 'error');
                    hasError = true;
                    return;
                }

                if (quantity <= 0 || quantity > 100) {
                    showAlert(`${index + 1}차 익절 매도 비율은 1% ~ 100% 범위로 설정해주세요.`, 'error');
                    hasError = true;
                    return;
                }

                totalQuantity += quantity;
            });

            if (hasError) {
                resetTradingButton();
                return;
            }

            if (totalQuantity > 100) {
                showAlert('전체 익절 매도 비율의 합이 100%를 초과할 수 없습니다.', 'error');
                resetTradingButton();
                return;
            }

            // 설정 적용
            updateTradingSettings();

            // 성공 메시지
            addLog(`✅ 매매 설정이 적용되었습니다!`, 'success');
            addLog(`💰 매수금액: $${tradingSettings.buyAmount}`, 'info');
            addLog(`📉 손절률: ${tradingSettings.stopLoss}%`, 'info');
            addLog(`📊 익절 단계: ${tradingSettings.profitTakingSteps.length}단계`, 'info');

            // 익절 단계별 상세 정보
            tradingSettings.profitTakingSteps.forEach((step, index) => {
                addLog(`   ${index + 1}차: ${step.rate}%에서 ${step.quantity}% 매도`, 'info');
            });

            // 성공 상태 표시
            btn.innerHTML = '✅ 적용 완료';
            btn.style.background = '#28a745';
            status.innerHTML = `<span style="color: #28a745;">✅ 매매 설정이 적용되었습니다. (${new Date().toLocaleTimeString()})</span>`;

            showAlert('매매 설정이 성공적으로 적용되었습니다!', 'success');

            // 3초 후 버튼 원상복구
            setTimeout(resetTradingButton, 3000);
        }

        // 매매 설정 버튼 원상복구
        function resetTradingButton() {
            const btn = document.getElementById('applyTradingBtn');
            btn.disabled = false;
            btn.innerHTML = '⚙️ 매매 설정 적용';
            btn.style.background = '#007bff';
        }

        // 모니터링 설정 업데이트 함수 (내부용)
        function updateMonitoringSettings() {
            monitoringSettings.minRiseRate = parseFloat(document.getElementById('minRiseRate').value) || 20;
            monitoringSettings.minVolume = parseFloat(document.getElementById('minVolume').value) || 80;
            monitoringSettings.maxStocks = parseInt(document.getElementById('maxMonitoringStocks').value) || 20;
        }

        // 모니터링 설정 적용 함수 (버튼 클릭용)
        function applyMonitoringSettings() {
            const btn = document.getElementById('applyMonitoringBtn');
            const status = document.getElementById('monitoringSettingsStatus');

            // 버튼 상태 변경
            btn.disabled = true;
            btn.innerHTML = '⏳ 적용 중...';
            btn.style.background = '#6c757d';

            // 설정 유효성 검사
            const minRiseRate = parseFloat(document.getElementById('minRiseRate').value);
            const minVolume = parseFloat(document.getElementById('minVolume').value);
            const maxStocks = parseInt(document.getElementById('maxMonitoringStocks').value);

            if (!minRiseRate || minRiseRate < 5 || minRiseRate > 100) {
                showAlert('최소 상승률은 5% ~ 100% 범위로 설정해주세요.', 'error');
                resetMonitoringButton();
                return;
            }

            if (!minVolume || minVolume < 10 || minVolume > 1000) {
                showAlert('최소 거래금액은 10만불 ~ 1000만불 범위로 설정해주세요.', 'error');
                resetMonitoringButton();
                return;
            }

            if (!maxStocks || maxStocks < 5 || maxStocks > 50) {
                showAlert('최대 모니터링 종목 수는 5개 ~ 50개 범위로 설정해주세요.', 'error');
                resetMonitoringButton();
                return;
            }

            // 설정 적용
            updateMonitoringSettings();

            // 성공 메시지
            addLog(`✅ 모니터링 설정이 적용되었습니다!`, 'success');
            addLog(`📈 최소 상승률: ${monitoringSettings.minRiseRate}%`, 'info');
            addLog(`💰 최소 거래금액: ${monitoringSettings.minVolume}만불`, 'info');
            addLog(`🎯 최대 모니터링 종목: ${monitoringSettings.maxStocks}개`, 'info');

            // 성공 상태 표시
            btn.innerHTML = '✅ 적용 완료';
            btn.style.background = '#28a745';
            status.innerHTML = `<span style="color: #28a745;">✅ 모니터링 설정이 적용되었습니다. (${new Date().toLocaleTimeString()})</span>`;

            showAlert('모니터링 설정이 성공적으로 적용되었습니다!', 'success');

            // 로그인된 상태라면 즉시 스캔 실행
            if (accessToken) {
                addLog('🔍 새로운 설정으로 즉시 스캔을 시작합니다...', 'info');
                status.innerHTML = `<span style="color: #007bff;">🔍 새로운 설정으로 스캔 중... (${new Date().toLocaleTimeString()})</span>`;

                setTimeout(() => {
                    scanStocksNow().then(() => {
                        status.innerHTML = `<span style="color: #28a745;">✅ 설정 적용 및 스캔 완료 (${new Date().toLocaleTimeString()})</span>`;
                    });
                }, 1000);
            }

            // 3초 후 버튼 원상복구
            setTimeout(resetMonitoringButton, 3000);
        }

        // 모니터링 설정 버튼 원상복구
        function resetMonitoringButton() {
            const btn = document.getElementById('applyMonitoringBtn');
            btn.disabled = false;
            btn.innerHTML = '⚙️ 모니터링 설정 적용';
            btn.style.background = '#007bff';
        }

        // 매매 설정 변경 감지
        function onTradingSettingsChange() {
            const status = document.getElementById('tradingSettingsStatus');
            if (status) {
                status.innerHTML = '<span style="color: #ffc107;">⚠️ 설정이 변경되었습니다. "매매 설정 적용" 버튼을 클릭하세요.</span>';
            }
        }

        // 모니터링 설정 변경 감지
        function onMonitoringSettingsChange() {
            const status = document.getElementById('monitoringSettingsStatus');
            if (status) {
                status.innerHTML = '<span style="color: #ffc107;">⚠️ 설정이 변경되었습니다. "모니터링 설정 적용" 버튼을 클릭하세요.</span>';
            }
        }

        // 매매 설정 변경 감지
        function onTradingSettingsChange() {
            const status = document.getElementById('tradingSettingsStatus');
            if (status) {
                status.innerHTML = '<span style="color: #ffc107;">⚠️ 설정이 변경되었습니다. "매매 설정 적용" 버튼을 클릭하세요.</span>';
            }
        }

        // 모니터링 설정 변경 감지
        function onMonitoringSettingsChange() {
            const status = document.getElementById('monitoringSettingsStatus');
            if (status) {
                status.innerHTML = '<span style="color: #ffc107;">⚠️ 설정이 변경되었습니다. "모니터링 설정 적용" 버튼을 클릭하세요.</span>';
            }
        }

        // 시장 전체 스캔 함수 (한국투자증권 조건검색 API 사용)
        async function scanStocksNow() {
            if (!accessToken) {
                addLog('❌ 로그인이 필요합니다.', 'error');
                return;
            }

            addLog('🔍 시장 전체 실시간 스캔 시작...', 'info');
            addLog(`📊 스캔 조건: 상승률 ${monitoringSettings.minRiseRate}%+, 거래액 ${monitoringSettings.minVolume}만불+`, 'info');

            try {
                // 시장 전체 스캔 API 호출
                const params = new URLSearchParams({
                    environment: config.environment,
                    accessToken: accessToken,
                    appkey: config.appKey,
                    appsecret: config.appSecret,
                    minRiseRate: monitoringSettings.minRiseRate,
                    minVolume: monitoringSettings.minVolume
                });

                const response = await fetch(`/api/us-stock/market-scan?${params}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.output && data.output.length > 0) {
                    const qualifiedStocks = [];

                    // API 응답 데이터 처리
                    for (const stock of data.output) {
                        const symbol = stock.symb || stock.symbol;
                        const changeRate = parseFloat(stock.rate || stock.prdy_ctrt || 0);
                        const currentPrice = parseFloat(stock.last || stock.curr || 0);
                        const volume = parseFloat(stock.tvol || stock.volume || 0);
                        const tradingValue = (currentPrice * volume) / 10000;

                        if (symbol && currentPrice > 0) {
                            qualifiedStocks.push({
                                symbol: symbol,
                                changeRate: changeRate,
                                tradingValue: tradingValue,
                                price: currentPrice,
                                volume: volume
                            });

                            addLog(`✅ ${symbol}: ${changeRate > 0 ? '+' : ''}${changeRate.toFixed(2)}%, $${currentPrice.toFixed(2)}, ${tradingValue.toFixed(0)}만불`, 'success');
                        }
                    }

                    // 상승률 순으로 정렬 (절댓값 기준)
                    qualifiedStocks.sort((a, b) => Math.abs(b.changeRate) - Math.abs(a.changeRate));

                    // 모니터링 리스트 업데이트
                    currentWatchList = qualifiedStocks.slice(0, monitoringSettings.maxStocks).map(stock => stock.symbol);

                    addLog(`🎯 시장 스캔 완료: ${qualifiedStocks.length}개 종목 발견, ${currentWatchList.length}개 선별`, 'success');

                    // 상위 5개 종목 상세 정보 표시
                    const top5 = qualifiedStocks.slice(0, 5);
                    top5.forEach((stock, index) => {
                        addLog(`🏆 ${index + 1}위: ${stock.symbol} ${stock.changeRate > 0 ? '+' : ''}${stock.changeRate.toFixed(2)}% ($${stock.price.toFixed(2)}, ${stock.tradingValue.toFixed(0)}만불)`, 'success');
                    });

                    updateWatchListUI();

                } else {
                    addLog('⚠️ 현재 조건을 충족하는 종목이 없습니다.', 'warning');
                    addLog(`💡 현재 조건: 상승률 ${monitoringSettings.minRiseRate}%+, 거래액 ${monitoringSettings.minVolume}만불+`, 'info');

                    // 조건 완화 제안
                    if (monitoringSettings.minRiseRate > 10) {
                        addLog('💡 조건을 완화해보세요: 상승률을 낮추거나 거래액 기준을 낮춰보세요.', 'info');
                    }

                    currentWatchList = [];
                    updateWatchListUI();
                }

            } catch (error) {
                addLog(`❌ 시장 스캔 오류: ${error.message}`, 'error');
                addLog('🔄 네트워크 연결을 확인하고 다시 시도해주세요.', 'warning');

                // 오류 발생 시 빈 리스트로 설정
                currentWatchList = [];
                updateWatchListUI();
            }
        }

        // 모니터링 종목 UI 업데이트 (동적 스캔 전용)
        function updateWatchListUI() {
            const watchListElement = document.getElementById('currentWatchList');
            const monitoringCountElement = document.getElementById('monitoringCount');

            if (currentWatchList.length === 0) {
                watchListElement.innerHTML = `
                    <div style="text-align: center; padding: 20px; color: #666;">
                        🌐 시장 전체 스캔 대기 중...<br>
                        <small>로그인 후 전체 시장에서 조건 충족 종목을 검색합니다.</small>
                    </div>
                `;
                monitoringCountElement.textContent = '0';
            } else {
                const stocksHtml = currentWatchList.map((symbol, index) =>
                    `<span style="background: ${index < 5 ? '#e8f5e8' : '#e3f2fd'}; padding: 3px 8px; border-radius: 4px; margin: 3px; display: inline-block; font-weight: ${index < 5 ? 'bold' : 'normal'};">
                        ${index < 5 ? '🏆' : '📊'} ${symbol}
                    </span>`
                ).join('');

                const lastScanTime = window.lastScanTime ?
                    new Date(window.lastScanTime).toLocaleTimeString('ko-KR') : '미실행';

                watchListElement.innerHTML = `
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <strong>🎯 총 ${currentWatchList.length}개 종목 (상위 5개 우선)</strong>
                            <small style="color: #666;">마지막 스캔: ${lastScanTime}</small>
                        </div>
                        <div>${stocksHtml}</div>
                    </div>
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px; color: #666;">
                        <div style="margin-bottom: 5px;">
                            <strong>📊 스캔 조건:</strong> 상승률 ${monitoringSettings.minRiseRate}%+ | 거래액 ${monitoringSettings.minVolume}만불+
                        </div>
                        <div>
                            <strong>🔄 업데이트:</strong> 5분마다 자동 스캔
                        </div>
                    </div>
                `;

                monitoringCountElement.textContent = currentWatchList.length;
            }
        }

        // 접근 토큰 발급 (Node.js 프록시 서버 사용)
        async function getAccessToken() {
            try {
                const response = await fetch('/api/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        environment: config.environment,
                        appkey: config.appKey,
                        appsecret: config.appSecret
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.access_token) {
                    accessToken = data.access_token;
                    addLog('✅ 접근 토큰 발급 성공', 'success');
                    return true;
                } else {
                    throw new Error(data.msg1 || data.error || '토큰 발급 실패');
                }
            } catch (error) {
                console.error('토큰 발급 오류:', error);
                addLog(`❌ 토큰 발급 실패: ${error.message}`, 'error');
                throw error;
            }
        }

        // 로그인 함수
        async function login() {
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.disabled = true;
            loginBtn.textContent = '인증 중...';

            try {
                // 입력값 검증
                config.environment = document.getElementById('environment').value;
                config.appKey = document.getElementById('appKey').value.trim();
                config.appSecret = document.getElementById('appSecret').value.trim();
                config.accountNumber = document.getElementById('accountNumber').value.trim();
                config.accountProduct = document.getElementById('accountProduct').value;

                if (!config.appKey || !config.appSecret || !config.accountNumber) {
                    throw new Error('모든 필드를 입력해주세요.');
                }

                if (config.accountNumber.length !== 8) {
                    throw new Error('계좌번호는 8자리여야 합니다.');
                }

                // 토큰 발급
                await getAccessToken();

                // 매매 설정 초기화
                updateTradingSettings();
                updateMonitoringSettings();

                // UI 전환
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('tradingSection').style.display = 'block';

                // 상태 업데이트
                document.getElementById('currentEnv').textContent =
                    config.environment === 'vps' ? '모의투자' : '실전투자';
                document.getElementById('currentAccount').textContent =
                    `${config.accountNumber}-${config.accountProduct}`;

                addLog('API 인증이 완료되었습니다.', 'success');
                addLog(`매매 설정: 매수금액 $${tradingSettings.buyAmount}, 손절 ${tradingSettings.stopLoss}%, 익절 ${tradingSettings.profitTakingSteps.length}단계`, 'info');
                addLog(`모니터링 설정: 동적 스캔, 상승률 ${monitoringSettings.minRiseRate}%+, 거래액 ${monitoringSettings.minVolume}만불+`, 'info');

                // 로그인 후 즉시 동적 스캔 실행
                addLog('🔍 로그인 완료! 조건 충족 종목 스캔을 시작합니다...', 'info');
                setTimeout(() => {
                    scanStocksNow();
                }, 2000); // 2초 후 스캔 시작

                showAlert('로그인이 완료되었습니다!', 'success');

                // 로그인 후 계좌잔액 조회
                setTimeout(() => {
                    getAccountBalance();
                }, 3000); // 3초 후 잔액 조회

            } catch (error) {
                showAlert(`로그인 실패: ${error.message}`, 'error');
                addLog(`로그인 실패: ${error.message}`, 'error');
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '🔐 API 인증 및 로그인';
            }
        }

        // 계좌잔액 조회 함수
        async function getAccountBalance() {
            if (!accessToken) {
                addLog('❌ 로그인이 필요합니다.', 'error');
                return;
            }

            try {
                addLog('💰 계좌잔액 조회 중...', 'info');

                const params = new URLSearchParams({
                    environment: config.environment,
                    accessToken: accessToken,
                    appkey: config.appKey,
                    appsecret: config.appSecret,
                    accountNumber: config.accountNumber,
                    accountProduct: config.accountProduct
                });

                const response = await fetch(`/api/account/balance?${params}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                // 디버깅을 위한 응답 데이터 로그
                console.log('💰 계좌잔액 API 응답:', data);
                addLog(`🔍 API 응답 구조: ${JSON.stringify(data, null, 2)}`, 'info');

                if (data.rt_cd === '0') {
                    let totalBalance = 0;
                    let availableBalance = 0;

                    // 다양한 응답 구조에 대응
                    if (data.output2 && data.output2.length > 0) {
                        const balance = data.output2[0];
                        totalBalance = parseFloat(balance.frcr_evlu_amt2 || balance.tot_evlu_amt || balance.evlu_amt || 0);
                        availableBalance = parseFloat(balance.frcr_buy_amt_smtl || balance.ord_psbl_frcr_amt || balance.buy_amt || 0);
                    } else if (data.output1 && data.output1.length > 0) {
                        const balance = data.output1[0];
                        totalBalance = parseFloat(balance.frcr_evlu_amt2 || balance.tot_evlu_amt || balance.evlu_amt || 0);
                        availableBalance = parseFloat(balance.frcr_buy_amt_smtl || balance.ord_psbl_frcr_amt || balance.buy_amt || 0);
                    } else if (data.output) {
                        const balance = data.output;
                        totalBalance = parseFloat(balance.frcr_evlu_amt2 || balance.tot_evlu_amt || balance.evlu_amt || 0);
                        availableBalance = parseFloat(balance.frcr_buy_amt_smtl || balance.ord_psbl_frcr_amt || balance.buy_amt || 0);
                    }

                    // UI 업데이트
                    document.getElementById('accountBalance').textContent = totalBalance > 0 ? `$${totalBalance.toLocaleString()}` : '정보 없음';
                    document.getElementById('availableBalance').textContent = availableBalance > 0 ? `$${availableBalance.toLocaleString()}` : '정보 없음';

                    if (totalBalance > 0 || availableBalance > 0) {
                        addLog(`💰 계좌잔액: $${totalBalance.toLocaleString()}, 가용금액: $${availableBalance.toLocaleString()}`, 'success');
                    } else {
                        addLog(`⚠️ 계좌잔액 정보를 찾을 수 없습니다. API 응답을 확인해주세요.`, 'warning');
                    }
                } else {
                    addLog(`⚠️ 계좌잔액 조회 실패: ${data.msg1 || data.message || '알 수 없는 오류'}`, 'warning');
                    document.getElementById('accountBalance').textContent = '조회 실패';
                    document.getElementById('availableBalance').textContent = '조회 실패';
                }

            } catch (error) {
                addLog(`❌ 계좌잔액 조회 오류: ${error.message}`, 'error');
                document.getElementById('accountBalance').textContent = '오류';
                document.getElementById('availableBalance').textContent = '오류';
            }
        }

        // 로그아웃 함수
        function logout() {
            if (isTrading) {
                stopTrading();
            }

            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('tradingSection').style.display = 'none';

            // 초기화
            accessToken = '';
            positions = [];

            // 입력 필드 초기화
            document.getElementById('appKey').value = '';
            document.getElementById('appSecret').value = '';
            document.getElementById('accountNumber').value = '';

            addLog('로그아웃되었습니다.', 'info');
        }

        // 미국 주식 현재가 조회 (Node.js 프록시 서버 사용)
        async function getUSStockPrice(symbol) {
            try {
                const params = new URLSearchParams({
                    environment: config.environment,
                    symbol: symbol,
                    accessToken: accessToken,
                    appkey: config.appKey,
                    appsecret: config.appSecret
                });

                const response = await fetch(`/api/us-stock/price?${params}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                return data;
            } catch (error) {
                console.error('주식 가격 조회 오류:', error);
                addLog(`${symbol} 가격 조회 실패: ${error.message}`, 'error');
                return null;
            }
        }

        // 미국 주식 1분봉 데이터 조회 (Node.js 프록시 서버 사용)
        async function getUSStockMinuteData(symbol) {
            try {
                const params = new URLSearchParams({
                    environment: config.environment,
                    symbol: symbol,
                    accessToken: accessToken,
                    appkey: config.appKey,
                    appsecret: config.appSecret
                });

                const response = await fetch(`/api/us-stock/minute?${params}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                return data;
            } catch (error) {
                console.error('1분봉 데이터 조회 오류:', error);
                addLog(`${symbol} 1분봉 조회 실패: ${error.message}`, 'error');
                return null;
            }
        }

        // 미국 주식 주문 (Node.js 프록시 서버 사용)
        async function placeUSStockOrder(symbol, quantity, orderType = 'buy') {
            try {
                const response = await fetch('/api/us-stock/order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        environment: config.environment,
                        accessToken: accessToken,
                        appkey: config.appKey,
                        appsecret: config.appSecret,
                        accountNumber: config.accountNumber,
                        accountProduct: config.accountProduct,
                        symbol: symbol,
                        quantity: quantity,
                        orderType: orderType
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                return data;
            } catch (error) {
                console.error('주문 오류:', error);
                addLog(`${symbol} 주문 실패: ${error.message}`, 'error');
                return null;
            }
        }

        // 매매 조건 확인
        function checkTradingConditions(stockData, minuteData, symbol) {
            if (!stockData || !minuteData) return false;

            try {
                const currentPrice = parseFloat(stockData.last || stockData.curr || 0);
                const volume = parseFloat(stockData.tvol || stockData.volume || 0);
                const changeRate = parseFloat(stockData.rate || stockData.prdy_ctrt || 0);

                // 거래액 계산 (가격 * 거래량)
                const tradingValue = currentPrice * volume;

                // 조건 1: 거래액 $80 이상
                const volumeCheck = tradingValue >= 80;

                // 조건 2: 일일 상승률 20% 이상
                const changeRateCheck = Math.abs(changeRate) >= 20;

                // 조건 3: 1분봉 상승률 5% 이상 확인
                let minuteChangeCheck = false;
                let highBreakCheck = false;

                if (minuteData.output2 && minuteData.output2.length >= 2) {
                    const latest = minuteData.output2[0];
                    const previous = minuteData.output2[1];

                    const latestClose = parseFloat(latest.clos || 0);
                    const latestHigh = parseFloat(latest.high || 0);
                    const previousClose = parseFloat(previous.clos || 0);
                    const previousHigh = parseFloat(previous.high || 0);

                    // 1분봉 상승률 계산
                    if (previousClose > 0) {
                        const minuteChange = ((latestClose - previousClose) / previousClose) * 100;
                        minuteChangeCheck = minuteChange >= 5;
                    }

                    // 전고점 돌파 확인
                    highBreakCheck = latestHigh > previousHigh;
                }

                const allConditionsMet = volumeCheck && changeRateCheck && minuteChangeCheck && highBreakCheck;

                // 디버깅 로그
                if (volumeCheck || changeRateCheck) {
                    addLog(`${symbol} 조건 체크: 거래액=$${tradingValue.toFixed(0)} (${volumeCheck ? '✓' : '✗'}), 상승률=${changeRate.toFixed(2)}% (${changeRateCheck ? '✓' : '✗'}), 1분봉=${minuteChangeCheck ? '✓' : '✗'}, 돌파=${highBreakCheck ? '✓' : '✗'}`, 'info');
                }

                return allConditionsMet;

            } catch (error) {
                addLog(`${symbol} 조건 확인 오류: ${error.message}`, 'error');
                return false;
            }
        }

        // 포지션 관리
        function updatePosition(symbol, action, price, quantity) {
            const existingPosition = positions.find(p => p.symbol === symbol);

            if (action === 'buy') {
                if (existingPosition) {
                    const totalQuantity = existingPosition.quantity + quantity;
                    existingPosition.avgPrice = ((existingPosition.avgPrice * existingPosition.quantity) + (price * quantity)) / totalQuantity;
                    existingPosition.quantity = totalQuantity;
                } else {
                    positions.push({
                        symbol: symbol,
                        quantity: quantity,
                        originalQuantity: quantity,
                        avgPrice: price,
                        highestPrice: price,
                        buyTime: new Date(),
                        profitTakingExecuted: [], // 실행된 익절 단계 추적
                        firstProfitTaken: false, // 1차 익절 실행 여부
                        breakEvenStopSet: false // 매입단가 손절 설정 여부
                    });
                }
            } else if (action === 'sell' && existingPosition) {
                existingPosition.quantity -= quantity;
                if (existingPosition.quantity <= 0) {
                    positions = positions.filter(p => p.symbol !== symbol);
                }
            }

            updatePositionUI();
        }

        // 포지션 UI 업데이트
        function updatePositionUI() {
            const positionPanel = document.getElementById('positionPanel');
            const positionCount = document.getElementById('positionCount');

            positionCount.textContent = positions.length;

            if (positions.length === 0) {
                positionPanel.innerHTML = `
                    <div style="text-align: center; color: #666; padding: 20px;">
                        현재 보유 중인 포지션이 없습니다.
                    </div>
                `;
            } else {
                let html = '';
                positions.forEach(position => {
                    const currentPrice = position.currentPrice || position.avgPrice;
                    const profit = (currentPrice - position.avgPrice) / position.avgPrice * 100;
                    const profitColor = profit >= 0 ? '#28a745' : '#dc3545';
                    const profitValue = (currentPrice - position.avgPrice) * position.quantity;

                    // 다음 익절 목표 찾기
                    const nextTarget = tradingSettings.profitTakingSteps.find(step =>
                        step.rate > profit && !position.profitTakingExecuted.includes(step.rate)
                    );

                    // 실행된 익절 단계 표시
                    const executedSteps = position.profitTakingExecuted.length > 0 ?
                        ` (익절: ${position.profitTakingExecuted.join(', ')}%)` : '';

                    html += `
                        <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                            <div style="display: flex; justify-content: space-between;">
                                <strong>${position.symbol}</strong>
                                <span style="color: ${profitColor};">${profit.toFixed(2)}% ($${profitValue.toFixed(2)})</span>
                            </div>
                            <div style="font-size: 12px; color: #666;">
                                수량: ${position.quantity}/${position.originalQuantity} | 평균가: $${position.avgPrice.toFixed(2)} | 현재가: $${currentPrice.toFixed(2)}
                            </div>
                            <div style="font-size: 11px; color: #888;">
                                ${position.firstProfitTaken ? '🔒 매입단가 손절' : '📉 ' + tradingSettings.stopLoss + '% 손절'}
                                ${nextTarget ? ` | 🎯 다음 익절: ${nextTarget.rate}%` : ''}${executedSteps}
                            </div>
                        </div>
                    `;
                });
                positionPanel.innerHTML = html;
            }
        }

        // 자동매매 시작
        function startTrading() {
            if (isTrading) return;

            isTrading = true;
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('tradingStatus').textContent = '실행 중';

            addLog('자동매매가 시작되었습니다.', 'success');

            // 3분 후 모니터링 시작 (개장 후 3분 대기)
            setTimeout(() => {
                if (isTrading) {
                    startMonitoring();
                    addLog('모니터링을 시작합니다. (개장 후 3분 경과)', 'info');
                }
            }, 3 * 60 * 1000); // 3분 = 180초
        }

        // 자동매매 중지
        function stopTrading() {
            if (!isTrading) return;

            isTrading = false;
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('tradingStatus').textContent = '중지됨';

            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
            }

            addLog('자동매매가 중지되었습니다.', 'warning');
        }

        // 모니터링 시작 (동적 스캔 전용)
        function startMonitoring() {
            // 모니터링 설정 업데이트
            updateMonitoringSettings();

            // 초기 스캔 실행
            scanStocksNow();

            // 1분마다 모니터링
            monitoringInterval = setInterval(async () => {
                if (!isTrading) return;

                // 5분마다 동적 스캔 업데이트
                const now = new Date();
                if (!window.lastScanTime || (now - window.lastScanTime) > 300000) {
                    addLog('🔄 모니터링 종목 업데이트 중...', 'info');
                    await scanStocksNow();
                    window.lastScanTime = now;
                }

                // 현재 모니터링 종목들 확인
                for (const symbol of currentWatchList) {
                    try {
                        await monitorStock(symbol);
                        // API 호출 제한을 위한 딜레이
                        await new Promise(resolve => setTimeout(resolve, 200));
                    } catch (error) {
                        addLog(`${symbol} 모니터링 오류: ${error.message}`, 'error');
                    }
                }

                // 포지션 관리 (손절/익절 확인)
                await managePositions();

            }, 60000); // 1분마다 실행
        }

        // 개별 주식 모니터링
        async function monitorStock(symbol) {
            try {
                const stockData = await getUSStockPrice(symbol);
                const minuteData = await getUSStockMinuteData(symbol);

                if (!stockData || !stockData.output) {
                    addLog(`${symbol} 데이터 조회 실패`, 'warning');
                    return;
                }

                const output = stockData.output;
                const currentPrice = parseFloat(output.last || output.curr || 0);
                const changeRate = parseFloat(output.rate || output.prdy_ctrt || 0);
                const volume = parseFloat(output.tvol || output.volume || 0);

                if (currentPrice === 0) {
                    addLog(`${symbol} 가격 정보 없음`, 'warning');
                    return;
                }

                // 이미 보유 중인 종목은 매수하지 않음
                const existingPosition = positions.find(p => p.symbol === symbol);
                if (existingPosition) {
                    return;
                }

                // 매매 조건 확인
                if (checkTradingConditions(output, minuteData, symbol)) {
                    // 설정된 매수 금액으로 수량 계산
                    updateTradingSettings(); // 최신 설정 적용
                    const targetAmount = tradingSettings.buyAmount;
                    const quantity = Math.floor(targetAmount / currentPrice);

                    if (quantity > 0) {
                        addLog(`${symbol} 매수 조건 충족! 주문 시도 중... (${quantity}주 @ $${currentPrice.toFixed(2)}, 총 $${targetAmount})`, 'info');

                        const orderResult = await placeUSStockOrder(symbol, quantity, 'buy');

                        if (orderResult && orderResult.rt_cd === '0') {
                            updatePosition(symbol, 'buy', currentPrice, quantity);
                            addLog(`${symbol} 매수 주문 완료: ${quantity}주 @ $${currentPrice.toFixed(2)} (총 $${(quantity * currentPrice).toFixed(2)})`, 'success');
                        } else {
                            const errorMsg = orderResult?.msg1 || orderResult?.msg_cd || '알 수 없는 오류';
                            addLog(`${symbol} 매수 주문 실패: ${errorMsg}`, 'error');
                        }
                    } else {
                        addLog(`${symbol} 매수 수량 계산 오류 (가격: $${currentPrice.toFixed(2)}, 목표금액: $${targetAmount})`, 'error');
                    }
                }

            } catch (error) {
                addLog(`${symbol} 모니터링 중 오류: ${error.message}`, 'error');
            }
        }

        // 포지션 관리 (다단계 손절/익절)
        async function managePositions() {
            if (positions.length === 0) return;

            updateTradingSettings(); // 최신 설정 적용

            for (let i = positions.length - 1; i >= 0; i--) {
                const position = positions[i];

                try {
                    const stockData = await getUSStockPrice(position.symbol);
                    if (!stockData || !stockData.output) {
                        addLog(`${position.symbol} 포지션 가격 조회 실패`, 'warning');
                        continue;
                    }

                    const output = stockData.output;
                    const currentPrice = parseFloat(output.last || output.curr || 0);

                    if (currentPrice === 0) {
                        addLog(`${position.symbol} 현재가 정보 없음`, 'warning');
                        continue;
                    }

                    position.currentPrice = currentPrice;

                    // 최고가 업데이트
                    if (currentPrice > position.highestPrice) {
                        position.highestPrice = currentPrice;
                        addLog(`${position.symbol} 신고가 갱신: $${currentPrice.toFixed(2)}`, 'info');
                    }

                    const profitRate = (currentPrice - position.avgPrice) / position.avgPrice * 100;
                    const drawdownFromHigh = (position.highestPrice - currentPrice) / position.highestPrice * 100;

                    let shouldSell = false;
                    let sellQuantity = 0;
                    let sellReason = '';

                    // 1. 손절 확인 (1차 익절 전: 설정값, 1차 익절 후: 매입단가)
                    const stopLossRate = position.firstProfitTaken ? 0 : tradingSettings.stopLoss;
                    if (profitRate <= stopLossRate) {
                        shouldSell = true;
                        sellQuantity = position.quantity;
                        sellReason = position.firstProfitTaken ?
                            `매입단가 손절 (${profitRate.toFixed(2)}%)` :
                            `손절 (${profitRate.toFixed(2)}%)`;
                    }
                    // 2. 다단계 익절 확인
                    else {
                        for (let step of tradingSettings.profitTakingSteps) {
                            if (profitRate >= step.rate && !position.profitTakingExecuted.includes(step.rate)) {
                                shouldSell = true;

                                // 매도 수량 계산 (원래 수량 기준 비율)
                                const sellRatio = step.quantity / 100;
                                sellQuantity = Math.floor(position.originalQuantity * sellRatio);

                                // 현재 보유 수량보다 많으면 전량 매도
                                if (sellQuantity > position.quantity) {
                                    sellQuantity = position.quantity;
                                }

                                sellReason = `${step.rate}% 익절 (${step.quantity}% 매도)`;
                                position.profitTakingExecuted.push(step.rate);

                                // 1차 익절 실행 표시
                                if (!position.firstProfitTaken) {
                                    position.firstProfitTaken = true;
                                    addLog(`${position.symbol} 1차 익절 완료! 나머지 물량 매입단가 손절로 전환`, 'info');
                                }
                                break;
                            }
                        }
                    }

                    // 3. 최고가 대비 -18% 최종 익절 (나머지 물량)
                    if (!shouldSell && drawdownFromHigh >= 18 && position.quantity > 0) {
                        shouldSell = true;
                        sellQuantity = position.quantity;
                        sellReason = `최고가 대비 -${drawdownFromHigh.toFixed(2)}% 최종익절`;
                    }

                    // 매도 실행
                    if (shouldSell && sellQuantity > 0) {
                        addLog(`${position.symbol} 매도 조건 충족: ${sellReason}`, 'warning');

                        const orderResult = await placeUSStockOrder(position.symbol, sellQuantity, 'sell');

                        if (orderResult && orderResult.rt_cd === '0') {
                            const sellValue = sellQuantity * currentPrice;
                            const profit = (currentPrice - position.avgPrice) * sellQuantity;
                            updatePosition(position.symbol, 'sell', currentPrice, sellQuantity);
                            addLog(`${position.symbol} 매도 완료: ${sellQuantity}주 @ $${currentPrice.toFixed(2)} (수익: $${profit.toFixed(2)}) - ${sellReason}`, 'success');
                        } else {
                            const errorMsg = orderResult?.msg1 || orderResult?.msg_cd || '알 수 없는 오류';
                            addLog(`${position.symbol} 매도 실패: ${errorMsg}`, 'error');
                        }
                    } else if (profitRate !== 0) {
                        // 포지션 상태 로그 (5분마다)
                        const now = new Date();
                        if (!position.lastLogTime || (now - position.lastLogTime) > 300000) {
                            const nextProfitTarget = tradingSettings.profitTakingSteps.find(step =>
                                step.rate > profitRate && !position.profitTakingExecuted.includes(step.rate)
                            );
                            const targetInfo = nextProfitTarget ? ` (다음 익절: ${nextProfitTarget.rate}%)` : '';
                            addLog(`${position.symbol} 포지션: ${profitRate.toFixed(2)}% (${position.quantity}주)${targetInfo}`, 'info');
                            position.lastLogTime = now;
                        }
                    }

                    // API 호출 제한을 위한 딜레이
                    await new Promise(resolve => setTimeout(resolve, 300));

                } catch (error) {
                    addLog(`${position.symbol} 포지션 관리 오류: ${error.message}`, 'error');
                }
            }

            updatePositionUI();
        }

        // 시스템 상태 업데이트
        function updateSystemStatus() {
            const now = new Date();
            const timeStr = now.toLocaleTimeString('ko-KR');

            // 서버 연결 상태 확인
            fetch('/api/health-check')
                .then(response => response.ok)
                .then(isHealthy => {
                    const statusElement = document.querySelector('.header div:last-child');
                    if (isHealthy) {
                        statusElement.innerHTML = `✅ Node.js 서버 연결됨 | 🔒 CORS 보안 해결됨 | 🌐 완전한 API 기능 지원 | ⏰ ${timeStr}`;
                        statusElement.style.color = '#90EE90';
                    } else {
                        statusElement.innerHTML = `❌ 서버 연결 오류 | ⏰ ${timeStr}`;
                        statusElement.style.color = '#FFB6C1';
                    }
                })
                .catch(() => {
                    const statusElement = document.querySelector('.header div:last-child');
                    statusElement.innerHTML = `⚠️ 서버 상태 확인 중... | ⏰ ${timeStr}`;
                    statusElement.style.color = '#FFD700';
                });
        }

        // 페이지 로드 시 초기화
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 Node.js 서버 기반 시스템이 초기화되었습니다.', 'success');
            addLog('✅ 완전한 API 기능이 지원됩니다.', 'info');
            addLog('🔍 동적 스캔 모드가 활성화되었습니다.', 'info');
            addLog('🔑 한국투자증권 API 키를 입력하고 로그인해주세요.', 'info');

            // 초기 모니터링 종목 UI 설정
            updateWatchListUI();

            // 시스템 상태 주기적 업데이트
            updateSystemStatus();
            setInterval(updateSystemStatus, 30000); // 30초마다 업데이트
        });

        // 토큰 갱신 (1시간마다)
        setInterval(async () => {
            if (accessToken && config.appKey) {
                try {
                    await getAccessToken();
                    addLog('접근 토큰이 갱신되었습니다.', 'info');
                } catch (error) {
                    addLog('토큰 갱신 실패: ' + error.message, 'error');
                }
            }
        }, 3600000); // 1시간

    </script>
</body>
</html>
